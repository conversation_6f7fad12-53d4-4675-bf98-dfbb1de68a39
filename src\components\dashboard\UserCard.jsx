"use client";
import React from "react";
import { useUser } from "@/context/UserContext";
import ProfilePicture from "@/components/ui/ProfilePicture";

function UserCard() {
  const { user, loading, error } = useUser();

  // Helper function to get greeting based on time
  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return "Good morning";
    if (hour < 17) return "Good afternoon";
    return "Good evening";
  };

  // Helper function to format date
  const formatDate = (dateString) => {
    if (!dateString) return null;
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      day: "2-digit",
      month: "short",
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    });
  };

  if (loading) {
    return (
      <div className="rounded-2xl border border-gray-200 bg-white px-8 py-8 item-center h-full">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-3/4 mb-4"></div>
          <div className="h-10 bg-gray-200 rounded w-1/2 mb-8"></div>
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-5"></div>
          <div className="h-4 bg-gray-200 rounded w-2/3"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-2xl border border-gray-200 bg-white px-8 py-8 item-center h-full">
        <div className="text-center text-red-500">
          <p>Error loading user data</p>
          <p className="text-sm text-gray-500 mt-2">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="rounded-2xl border border-gray-200 bg-white px-8 py-8 item-center h-full">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h2 className="text-xl font-medium">
              <span className="text-blue-600 font-semibold">Hello,</span>
              <span className="text-sky-500 ml-1">{getGreeting()}</span>
            </h2>
            <h1 className="text-4xl capitalize font-extrabold text-gray-800 mt-3">
              {user?.displayName || "User"}
            </h1>
            <div className="mt-8">
              <span className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-500 to-sky-500 text-white px-3 py-1 rounded-lg text-sm font-medium">
                ID: {user?.id || "N/A"}
              </span>
            </div>
            <p className="text-gray-500 text-sm mt-5">
              {user?.createdAt ? (
                <>
                  Member since <span className="italic">{formatDate(user.createdAt)}</span>
                </>
              ) : (
                "Welcome to Money Chain"
              )}
            </p>
          </div>

          {/* Profile Picture */}
          <div className="ml-6">
            <ProfilePicture
              src={user?.avatarPath}
              alt={user?.displayName}
              initials={user?.initials}
              size="xl"
              className="border-4 border-white shadow-lg"
            />
          </div>
        </div>
      </div>
    </>
  );
}

export default UserCard;
