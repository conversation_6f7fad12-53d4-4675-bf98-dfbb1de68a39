const baseURL = `${process.env.NEXT_PUBLIC_API_BASE_URL}`;

const ApiPath = {
  baseURL,

  // General settings path
  getSettings: `/get-settings`,

  // Auth path
  signin: `/auth/merchant/login`,

  // Notification
  getNotifications: `/get-notifications`,
  markNotificationAsRead: `/mark-as-read-notification`,

  // Dashboard
  allWallets: `/merchant/wallets`,
  recentTransition: `/merchant/transactions`,
  qrCode: `/merchant/qrcode`,
  allWallets: `/merchant/wallets`,

  // Support
  createSupportTicket: `/merchant/ticket`,
  getSupportTickets: `/merchant/ticket`,
  getSupportTicketChat: (id) => `/merchant/ticket/${id}`,
  replySupportTicket: (id) => `merchant/ticket/reply/${id}`,

  // Wallet
  createWallet: `/merchant/wallets`,
  deleteWallet: `/merchant/wallets/`,

  // Withdraw
  withdrawMethods: `/merchant/withdraw-accounts/methods/list`,
  withdrawAccounts: `/merchant/withdraw-accounts`,
  createWithdrawAccount: `/merchant/withdraw-accounts`,
  updateWithdrawAccount: `/merchant/withdraw-accounts/`,
  withdrawRequest: `/merchant/withdraw`,
  deleteWithdrawAccount: `/merchant/withdraw-accounts/`,

  // API access key
  apiKeys: `/merchant/access-keys`,
  generateApiKey: `/merchant/access-keys/regenerate`,

  // Settings
  getCountries: `/get-countries`,
  postProfile: `/merchant/settings/profile`,
  getProfile: `/merchant/settings/profile`,
  changePassword: `/merchant/settings/change-password`,
};

export default ApiPath