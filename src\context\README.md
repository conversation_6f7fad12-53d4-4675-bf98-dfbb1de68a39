# User Context Documentation

## Overview
The UserContext provides a centralized way to manage user profile data throughout the application. It fetches user information from the API and makes it available to all components within the merchant layout.

## Features
- Fetches user profile data from `/merchant/settings/profile` API endpoint
- Provides loading and error states
- Handles profile picture display with fallback to initials
- Automatically formats user data for easy consumption
- Includes refresh and update functions for real-time updates

## Usage

### Basic Usage
```jsx
import { useUser } from "@/context/UserContext";

function MyComponent() {
  const { user, loading, error } = useUser();

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div>
      <h1>Hello, {user.displayName}!</h1>
      <p>Email: {user.email}</p>
    </div>
  );
}
```

### Available User Properties
- `id`: User ID
- `firstName`: First name
- `lastName`: Last name
- `username`: Username
- `email`: Email address
- `phone`: Phone number
- `avatarPath`: Profile picture URL
- `fullName`: Computed full name (firstName + lastName)
- `displayName`: Best available display name (username or fullName)
- `initials`: User initials for avatar fallback

### Context Methods
- `refreshUser()`: Refetch user data from API
- `updateUser(data)`: Update user data locally (useful after profile updates)

## Components Using UserContext
- `UserCard`: Dashboard user card with profile picture
- `UserDropdown`: Header dropdown with user info and profile picture
- `ProfilePicture`: Reusable component for displaying user avatars

## Integration
The UserProvider is integrated in the merchant layout (`src/app/(merchant)/layout.js`) so all merchant pages have access to user data.
