import React from "react";
import Image from "next/image";

const ProfilePicture = ({ 
  src, 
  alt = "Profile", 
  initials = "U", 
  size = "md", 
  className = "" 
}) => {
  // Size configurations
  const sizeClasses = {
    sm: "w-8 h-8 text-sm",
    md: "w-11 h-11 text-lg",
    lg: "w-16 h-16 text-xl",
    xl: "w-20 h-20 text-2xl",
  };

  const sizeConfig = {
    sm: { width: 32, height: 32 },
    md: { width: 44, height: 44 },
    lg: { width: 64, height: 64 },
    xl: { width: 80, height: 80 },
  };

  const currentSize = sizeClasses[size] || sizeClasses.md;
  const imageSize = sizeConfig[size] || sizeConfig.md;

  return (
    <div 
      className={`
        ${currentSize} 
        rounded-full 
        overflow-hidden 
        bg-gray-200 
        dark:bg-gray-600 
        flex 
        items-center 
        justify-center 
        ${className}
      `}
    >
      {src ? (
        <Image
          width={imageSize.width}
          height={imageSize.height}
          src={src}
          alt={alt}
          className="w-full h-full object-cover"
        />
      ) : (
        <span className={`font-bold text-gray-600 dark:text-gray-300`}>
          {initials}
        </span>
      )}
    </div>
  );
};

export default ProfilePicture;
